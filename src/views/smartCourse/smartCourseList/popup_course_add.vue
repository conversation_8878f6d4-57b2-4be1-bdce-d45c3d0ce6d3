<template>
  <el-dialog v-model="dialogVisible" title="新增课程" width="600" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="课程类别" prop="courseType">
        <el-radio-group v-model="form.courseType">
          <el-radio value="1">教务课程</el-radio>
          <el-radio value="2">自主课程</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-select v-model="form.courseName" placeholder="请选择课程名称" @change="onBookNameChange">
          <el-option v-for="item in courseNameList" :key="item.courseId" :label="item.courseName" :value="item.courseId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程编码" prop="courseCode">
        <el-input show-word-limit maxlength="50" v-model="form.courseCode" type="text" autocomplete="off" placeholder="请输入课程编码" />
      </el-form-item>
<!--      <el-form-item label="所属专业大类" prop="categoryId">-->
<!--        <el-input show-word-limit maxlength="50" v-model="form.categoryName" type="text" autocomplete="off" placeholder="请输入所属专业大类" />-->
<!--      </el-form-item>-->
      <el-form-item label="所属专业大类" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择专业大类">
          <el-option v-for="item in categoryList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课时">
        <el-input show-word-limit maxlength="11" v-model="form.hour" type="text" autocomplete="off" placeholder="请输入该课程所学课时数" />
      </el-form-item>
      <el-form-item label="关联云教材">
        <el-select v-model="form.bookId" placeholder="请选择关联云教材">
          <el-option v-for="item in bookList" :key="item.bookId" :label="item.bookName" :value="item.bookId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程描述" prop="description">
        <el-input v-model="form.description" :rows="3" type="textarea" show-word-limit="true" maxlength="200" placeholder="请输入考试要求" />
      </el-form-item>
      <el-form-item label="添加课程封面" prop="coverImageUrl">
        <!-- <el-upload
          class="avatar-uploader"
          v-model:file-list="form.fileList"
          :action="uploadUrl"
          :headers="headers"
          :before-upload="beforeAvatarUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
        >
          <img v-if="imageUrl" :src="imageUrl" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload> -->
        <el-upload
          v-model:file-list="form.coverImageUrl"
          :action="uploadUrl"
          :headers="headers"
          :before-upload="beforeAvatarUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          list-type="picture-card"
          :auto-upload="true"
        >
          <el-icon><Plus /></el-icon>

          <template #file="{ file }">
            <div>
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <el-icon><zoom-in /></el-icon>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
                  <el-icon><Download /></el-icon>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <el-icon><Delete /></el-icon>
                </span>
              </span>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisibleImg">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { getCoursePlanList } from '@/api/edu/moocSmartCoursePlan'
import { getAllBySchoolId } from '@/api/book/book'
import { selectEduAcademyList } from "@/api/basic/school.js";


const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const dialogImageUrl = ref('')
const disabled = ref(false)
const dialogVisible = ref(false)
const dialogVisibleImg = ref(false)
const formRef = ref(null)
const form = ref({
  courseType:'1',
})
// const rules = ref({
//   bookName: [{ required: true, message: '课程名称不能为空', trigger: 'change' }],
//   kslx: [{ required: true, message: '课程类别不能为空', trigger: 'change' }],
//   bookName: [{ required: true, message: '课程编码不能为空', trigger: 'change' }],
//   ksbj: [{ required: true, message: '所属专业大类不能为空', trigger: 'change' }]
// })
const courseNameList = ref([]);
const bookList = ref([]);
const categoryList = ref([]);


// 开启弹窗
const open = () => {
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
}
// 上传
const beforeAvatarUpload = rawFile => {
  console.log(rawFile)
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
    ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error('上传失败！文件大小不能超过 60MB!')
    return false
  }
  return true
}
// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      // 上传文件数组
      const list = fileList.value.map(item => item.response.data)
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const handleRemove = (file) => {
  console.log(file)
  console.log(form.value.fileList)
  form.value.fileList = form.value.fileList.filter(item => item.uid !== file.uid)
}

const handlePictureCardPreview = file => {
  dialogImageUrl.value = file.url
  dialogVisibleImg.value = true
}

const handleDownload = file => {
  console.log(file)
}
const handleClose = () => {
  dialogVisible.value = false
}
// 查询教学计划接口
const getCoursePlan = () => {
  getCoursePlanList().then(res => {
    courseNameList.value = res.data;
    console.log(courseNameList.value);
  })
}
// 查询所属专业大类
const getCategoryList = () => {
  selectEduAcademyList({}).then(res => {
    categoryList.value = res.data;
  })
}


// 查询云教材接口
const getBookList = () => {
  getAllBySchoolId({}).then(res => {
    bookList.value = res.data;
  })
}

// 课程名称改变时带出信息
const onBookNameChange = (val) => {
  courseNameList.value.forEach(item => {
    if (item.courseId == val) {
      form.value.courseName = item.courseName;
      form.value.courseCode = item.courseCode;
      form.value.categoryId = item.categoryId;
      form.value.hour = item.hour;
      form.value.description = item.description;
      form.value.bookId = item.bookId;
      form.value.bookName = item.bookName;
    }
  })
}

function initList(){
  getCoursePlan();
  getBookList();
  getCategoryList();
}

onMounted(() => {
  initList();
})
defineExpose({
  open
})
</script>
<style lang="scss" scoped></style>
