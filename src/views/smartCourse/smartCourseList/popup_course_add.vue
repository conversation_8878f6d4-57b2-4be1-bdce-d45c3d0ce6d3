<template>
  <el-dialog v-model="dialogVisible" title="新增课程" width="600" :before-close="handleClose" style="margin-top: 8vh !important">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-ruleForm" :rules="rules">
      <el-form-item label="课程类别" prop="courseType">
        <el-radio-group v-model="form.courseType">
          <el-radio value="1">教务课程</el-radio>
          <el-radio value="2">自主课程</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-select v-model="form.courseName" placeholder="请选择课程名称" @change="onBookNameChange">
          <el-option v-for="item in courseNameList" :key="item.courseId" :label="item.courseName" :value="item.courseId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程编码" prop="courseCode">
        <el-input show-word-limit maxlength="50" v-model="form.courseCode" type="text" autocomplete="off" placeholder="请输入课程编码" />
      </el-form-item>
<!--      <el-form-item label="所属专业大类" prop="categoryId">-->
<!--        <el-input show-word-limit maxlength="50" v-model="form.categoryName" type="text" autocomplete="off" placeholder="请输入所属专业大类" />-->
<!--      </el-form-item>-->
      <el-form-item label="所属专业大类" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择专业大类">
          <el-option v-for="item in categoryList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课时">
        <el-input show-word-limit maxlength="11" v-model="form.hour" type="text" autocomplete="off" placeholder="请输入该课程所学课时数" />
      </el-form-item>
      <el-form-item label="关联云教材">
        <el-select v-model="form.bookId" placeholder="请选择关联云教材">
          <el-option v-for="item in bookList" :key="item.bookId" :label="item.bookName" :value="item.bookId" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程描述" prop="description">
        <el-input v-model="form.description" :rows="3" type="textarea" show-word-limit="true" maxlength="200" placeholder="请输入考试要求" />
      </el-form-item>
      <el-form-item label="添加课程封面" prop="coverImageUrl">

        <div class="upload-container">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="headers"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :on-success="handleSuccess"
            :auto-upload="true"
          >
            <img v-if="form.coverImageUrl" :src="form.coverImageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div v-if="form.coverImageUrl" class="upload-actions">
            <el-button type="danger" size="small" @click="handleRemove">删除图片</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmFrom">确定</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { getCoursePlanList } from '@/api/edu/moocSmartCoursePlan'
import { getAllBySchoolId } from '@/api/book/book'
import { selectEduAcademyList } from "@/api/basic/school.js";


const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const dialogVisible = ref(false)
const formRef = ref(null)
const form = ref({
  courseType:'1',
  coverImageUrl: ''
})
// const rules = ref({
//   bookName: [{ required: true, message: '课程名称不能为空', trigger: 'change' }],
//   kslx: [{ required: true, message: '课程类别不能为空', trigger: 'change' }],
//   bookName: [{ required: true, message: '课程编码不能为空', trigger: 'change' }],
//   ksbj: [{ required: true, message: '所属专业大类不能为空', trigger: 'change' }]
// })
const courseNameList = ref([]);
const bookList = ref([]);
const categoryList = ref([]);


// 开启弹窗
const open = () => {
  dialogVisible.value = true
  // 清空校验
  if (formRef.value) formRef.value.resetFields()
}
// 上传
const beforeAvatarUpload = rawFile => {
  console.log(rawFile)
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/png' && rawFile.type !== 'application/pdf') {
    ElMessage.error('上传失败！请上传 jpg, jpeg, png, pdf格式文件！')
    return false
  } else if (rawFile.size / 1024 / 1024 > 60) {
    ElMessage.error('上传失败！文件大小不能超过 60MB!')
    return false
  }
  return true
}
// 上传成功回调
const handleSuccess = (response, file) => {
  if (response.code === 200) {
    form.value.coverImageUrl = response.data
    ElMessage.success('上传成功')
  } else {
    ElMessage.error('上传失败')
  }
}

// 确定
const confirmFrom = () => {
  formRef.value.validate(valid => {
    if (valid) {
      // 单个文件URL
      console.log('封面图片URL:', form.value.coverImageUrl)
      console.log('submit!')
      dialogVisible.value = false
    } else {
      console.log('error submit!')
    }
  })
}
// 移除图片
const handleRemove = () => {
  form.value.coverImageUrl = ''
  ElMessage.success('图片已移除')
}


const handleClose = () => {
  dialogVisible.value = false
}
// 查询教学计划接口
const getCoursePlan = () => {
  getCoursePlanList().then(res => {
    courseNameList.value = res.data;
    console.log(courseNameList.value);
  })
}
// 查询所属专业大类
const getCategoryList = () => {
  selectEduAcademyList({}).then(res => {
    categoryList.value = res.data;
  })
}


// 查询云教材接口
const getBookList = () => {
  getAllBySchoolId({}).then(res => {
    bookList.value = res.data;
  })
}

// 课程名称改变时带出信息
const onBookNameChange = (val) => {
  courseNameList.value.forEach(item => {
    if (item.courseId == val) {
      form.value.courseName = item.courseName;
      form.value.courseCode = item.courseCode;
      form.value.categoryId = item.categoryId;
      form.value.hour = item.hour;
      form.value.description = item.description;
      form.value.bookId = item.bookId;
      form.value.bookName = item.bookName;
    }
  })
}

function initList(){
  getCoursePlan();
  getBookList();
  getCategoryList();
}

onMounted(() => {
  initList();
})
defineExpose({
  open
})
</script>
<style lang="scss" scoped>
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
  }
}

.upload-actions {
  margin-top: 8px;
}
</style>
