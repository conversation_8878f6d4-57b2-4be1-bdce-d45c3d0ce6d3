import { createWebHistory, createRouter } from 'vue-router';
/* Layout */
import Layout from '@/layout';
import LayoutSub from '@/layoutSub';
import user from '@/views/user/home/<USER>';
import Reader from '@/views/reader/index.vue';
import SimplifiedReader from '@/views/reader/simplified.vue';
import RunCode from '@/views/runCode/index.vue';
import { DUTP_FONT_URL } from '@/utils/constant.js';
import { loadDutpFonts } from '@/utils/dutpFontUtils';


// 公共路由
export const constantRoutes = [
  {
    path: '/home',
    component: () => import('@/views/index'),
    name: 'Home',
    meta: { title: '首页' },
    beforeEnter: () => {
      // 预加出版社载字体
      // loadDutpFonts(['FZCHYK', 'FZCYK']);
    },
  },
  {
    path: '/page',
    component: () => import('@/views/page'),
    name: 'Page',
  },
  {
    path: '/sub-page',
    component: () => import('@/layoutSub/index'),
    name: 'SubPageLayout',
    hidden: true,
    children: [
      {
        path: '/search',
        component: () => import('@/views/search/index'),
        name: 'Search',
        meta: { title: '' },
      },
      {
        path: '/search-advanced',
        component: () => import('@/views/searchAdvanced/index'),
        name: 'SearchAdvanced',
        meta: { title: '' },
      },
      {
        path: '/search-catalog',
        component: () => import('@/views/searchCatalog/index'),
        name: 'SearchCatalog',
        meta: { title: '' },
      },
      {
        path: '/search-catalog',
        component: () => import('@/views/searchCatalog/index'),
        name: 'SearchCatalog',
        meta: { title: '' },
      },
      {
        path: '/search-result',
        component: () => import('@/views/search/searchResult/index'),
        name: 'SearchResult',
        meta: { title: '' },
      },
      {
        path: '/book-detail',
        component: () => import('@/views/book/bookDetail/index'),
        name: 'BookDetail',
        meta: { title: '' },
      },
      {
        path: '/book-natural-resources',
        component: () => import('@/views/book/bookNaturalResources/index'),
        name: 'BookNaturalResources',
        meta: { title: '' },
      },
      {
        path: '/special-subject-books-list',
        component: () => import('@/views/home/<USER>/index'),
        name: 'SpecialSubjectBooksList',
        meta: { title: '专题教材列表' },
      },
      {
        path: '/contact-submit-article',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'ContactSubmitArticle',
        meta: { title: '我要投稿' },
      },
      {
        path: '/contact-us',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'ContactUs',
        meta: { title: '联系我们' },
      },
      {
        path: '/copyright-notice',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'CopyrightNotice',
        meta: { title: '版权声明' },
      },
      {
        path: '/site-instructions',
        component: () => import('@/views/home/<USER>/index'),
        hidden: false,
        name: 'SiteInstructions',
        meta: { title: '站点使用说明' },
      },
      {
        path: '/home-partners-school',
        component: () => import('@/views/home/<USER>/partnersSchoolComp/index'),
        name: 'PartnersSchool',
        meta: { title: '合作院校' },
      },
      {
        path: '/partner-school-books',
        component: () => import('@/views/home/<USER>/schoolBooksList/index'),
        name: 'HomeSchoolBooks',
        meta: { title: '合作院校图书列表' },
      },
    ],
  },
  {
    path: '/layout-open-course',
    component: () => import('@/layoutOpenCourse/index'),
    name: 'LayoutOpenCourse',
    hidden: true,
    props: (route) => ({ showNewHeader: route.meta.showNewHeader }),
    children: [
      {
        path: '/open-course-list',
        component: () => import('@/views/openCourse/openCourseList/index'),
        name: 'OpenCourseList',
        meta: { 
          title: '公开课列表',
        }
      },
      {
        path: '/smart-course-list',
        component: () => import('@/views/smartCourse/smartCourseList/index'),
        name: 'smartCourseList',
        meta: {
            title: '互动课堂'
        }
      }
    ],
  },
  {
    path: '/layout-open-course-no-banner',
    component: () => import('@/layoutOpenCourse/indexNoBanner'),
    name: 'LayoutOpenCourseNoBanner',
    hidden: true,
    children: [
      {
        path: '/openCourse',
        component: () => import('@/views/openCourse/index'),
        name: 'openCourse',
        meta: { title: '公开课' },
      },
      {
        path: '/open-course-detail',
        component: () => import('@/views/openCourse/openCourseDetail/index'),
        name: 'OpenCourseDetail',
        meta: { title: '公开课详情' }
      },
      {
        path: '/open-course-to-examine',
        component: () => import('@/views/openCourse/openCourseToExamine/index'),
        name: 'OpenCourseToExamine',
        meta: { 
          title: '公开课审核',
        }
      },    {
        path: '/my-open-classes',
        name: 'MyOpenClassList',
        component: () => import('@/views/openClass/myOpenClassList/index.vue'),
        meta: {
          title: '我的公开课',
          showNewHeader: true
        }
      },
      {
        path: '/learn-open-class',
        name: 'LearnOpenClass',
        component: () => import('@/views/openClass/learnOpenClass/index.vue'),
        meta: {
          title: '公开课学习',
          showNewHeader: true
        },
      },
      {
        path: '/paper/:paperId',
        name: 'SmartCoursePaper',
        component: () => import('@/views/smartCourse/paper/index.vue'),
        meta: { title: '试卷' }
      },
      {
        path: '/paper/review/:submissionId',
        name: 'SmartCoursePaperReview',
        component: () => import('@/views/smartCourse/paper/review.vue'),
        meta: { title: '作业批改' }
      },
      {
        path: '/extension/:extensionId',
        name: 'SmartCourseExtension',
        component: () => import('@/views/smartCourse/extension/index.vue'),
        meta: { title: '拓展作业' }
      },
      {
        path: '/lesson-evaluation/:lessonId',
        name: 'LessonEvaluation',
        component: () => import('@/views/smartCourse/lessonEvaluation/index.vue'),
        meta: { title: '课堂评价' }
      },
      {
        path: '/survey/:surveyId',
        component: () => import('@/views/smartCourse/survey/index'),
        name: 'SmartCourseSurvey',
        meta: { title: '问卷调查' }
      },
    ],
  },
  
  // 添加教师空间路由
  {
    path: '/teacher-space',
    name: 'TeacherSpace',
    meta: { title: '教师空间' },
    component: () => import('@/layoutOpenCourse/teacherBanner'),
    children: [
      {
        path: 'question',
        component: () => import('@/views/teacherSpace/teacherQuestion/index.vue'),
        name: 'TeacherQuestion',
        meta: { title: '题库管理' }
      },     {
        path: 'teacher-resource',
        component: () => import('@/views/teacherSpace/teacherResource/index.vue'),
        name: 'TeacherResource',
        meta: { title: '资源管理', keepAlive: true }
      }
    ]
  },
  {
    path: '/about-us',
    component: () => import('@/views/home/<USER>/index.vue'),
    hidden: true,
  },
  {
    path: '/book-codes',
    component: () => import('@/views/home/<USER>/bookCodes/index'),
    name: 'BookCodes',
    meta: { title: '购书码' },
  },
  {
    path: '/reader',
    component: Reader,
    hidden: false,
    name: 'Reader',
    meta: { title: '阅读器' },
  },
  {
    path: '/simplifiedreader',

    component: () => import('@/views/reader/simplified'),
    hidden: false,
    name: 'SimplifiedReader',
    meta: { title: '预览章节内容' },
  },
  {
    path: '/runCode',
    component: RunCode,
    hidden: false,
    name: 'runCode',
    meta: { title: '阅读器-运行代码' },
  },
  {
    path: '/user',
    component: user, // 使用 Layout 或其他主页面布局组件
    redirect: '/basic-information', // 默认跳转到 "基本信息" 页面
    children: [
      // 顶部菜单列表
      {
        path: '/my-message',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyMessage',
        meta: { title: '我的消息' },
      },
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BasicInformation',
        meta: { title: '基本信息' },
      },
      {
        path: '/purchase-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'PurchaseHistory',
        meta: { title: '我的书架' },
      },
      {
        path: '/collect',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Collect',
        meta: { title: '我的收藏' },
      },
      {
        path: '/browsing-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BrowsingHistory',
        meta: { title: '浏览历史' },
      },
      {
        path: '/book-purchase-code',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'BookPurchaseCode',
        meta: { title: '我的购书码' },
      },
      {
        path: '/my-order',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyOrder',
        meta: { title: '我的订单' },
      },
      {
        path: '/error-correction',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'ErrorCorrection',
        meta: { title: '我的慧点' },
      },
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'StudyReport',
        meta: { title: '学习报告' },
      },
      {
        path: '/myCorrection',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyCorrection',
        meta: { title: '我的纠错' },
      },
      {
        path: '/invoice-header',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'InvoiceHeader',
        meta: { title: '发票中心' },
      },
      // 中部菜单列表
      {
        path: '/basic-information',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'MyClass',
        meta: { title: '我的班级' },
      },
      {
        path: '/identity',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Identity',
        meta: { title: '身份认证' },
      },
      {
        path: '/application-history',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'ApplicationHistory',
        meta: { title: '申请历史' },
      },

      // 底部菜单列表
      {
        path: '/feedback',
        component: () => import('@/views/user/home/<USER>/index.vue'),
        name: 'Feedback',
        meta: { title: '意见反馈' },
      },
    ],
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/user-agreement',
    component: () => import('@/views/userAgreement'),
    hidden: true,
  },
  {
    path: '/privacy-policy',
    component: () => import('@/views/privacyPolicy'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '/agreement',
    component: () => import('@/views/user/home/<USER>/agreement.vue'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },

];
// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // 是否滚动行为
  scrollBehavior(to, from, savePosition) {
    if (savePosition) {
      return savePosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
